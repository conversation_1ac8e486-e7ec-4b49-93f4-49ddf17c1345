# BanditGUI - Complete Documentation

## Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Architecture](#architecture)
4. [Installation](#installation)
5. [Usage Guide](#usage-guide)
6. [API Documentation](#api-documentation)
7. [Development Guide](#development-guide)
8. [Configuration](#configuration)
9. [Troubleshooting](#troubleshooting)
10. [Contributing](#contributing)
11. [License](#license)

## Overview

BanditGUI is a modern web-based graphical interface for the popular OverTheWire Bandit wargame. It transforms the traditional command-line experience into an intuitive, feature-rich web application that makes cybersecurity learning more accessible and engaging.

### What is Bandit?

Bandit is a wargame offered by OverTheWire that teaches security concepts through progressive levels, each requiring different skills to solve. BanditGUI enhances this experience by providing:

- A sleek web interface for SSH connections
- Integrated terminal emulation
- Level hints and guidance
- Progress tracking
- Built-in chat functionality

### Target Audience

- **Students** learning cybersecurity fundamentals
- **Educators** teaching security concepts
- **Professionals** practicing penetration testing skills
- **Hobbyists** interested in security challenges

## Features

### 🚀 Core Features

- **Web-based SSH Terminal**: Full terminal emulation using XTerm.js
- **Multi-level Support**: Complete integration with all Bandit levels
- **Real-time Connection**: Direct SSH connections to OverTheWire servers
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Progress Tracking**: Keep track of completed levels and achievements

### 🎯 Enhanced Features

- **Interactive Chat System**: Built-in chat for collaboration and hints
- **Smart Hints System**: Contextual hints and tips for each level
- **Command History**: Persistent command history across sessions
- **Geek Quotes**: Motivational quotes from tech personalities
- **Level Information**: Detailed information about each challenge
- **Data Persistence**: Save progress and session data

### 🔧 Technical Features

- **Cross-platform**: Runs on Windows, macOS, and Linux
- **Easy Installation**: Automated setup scripts for all platforms
- **Modular Architecture**: Clean, maintainable Python codebase
- **RESTful API**: Well-documented API for integration
- **Security-focused**: Secure session management and data handling

## Architecture

### System Overview

BanditGUI follows a modular Flask-based architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Web Browser (Client)                     │
├─────────────────────────────────────────────────────────────┤
│                 Flask Web Application                       │
├─────────────────┬─────────────────┬─────────────────────────┤
│   SSH Manager   │ Terminal Manager│    Chat Manager         │
├─────────────────┼─────────────────┼─────────────────────────┤
│  Config System  │  Data Manager   │   Utilities             │
├─────────────────┴─────────────────┴─────────────────────────┤
│              External Dependencies                          │
│           (XTerm.js, SSH, OverTheWire)                     │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. Application Layer (`banditgui/app.py`)

- Main Flask application entry point
- Route definitions and request handling
- Session management and authentication
- WebSocket endpoints for real-time communication

#### 2. SSH Management (`banditgui/ssh/`)

- **ssh_manager.py**: Handles SSH connections to Bandit servers
- Connection pooling and session management
- Secure credential handling
- Connection state monitoring

#### 3. Terminal Management (`banditgui/terminal/`)

- **terminal_manager.py**: Web terminal emulation logic
- XTerm.js integration and communication
- Command execution and output streaming
- Terminal state persistence

#### 4. Chat System (`banditgui/chat/`)

- **chat_manager.py**: Real-time chat functionality
- WebSocket-based messaging
- Chat room management
- Message persistence and history

#### 5. Configuration (`banditgui/config/`)

- **settings.py**: Application configuration management
- **logging.py**: Centralized logging configuration
- Environment-specific settings
- Security configurations

#### 6. Data Management (`banditgui/data/`)

- **all_data.json**: Comprehensive game data
- **commands_data.json**: Command reference and hints
- **levels_info.json**: Level-specific information
- **geek_quotes.json**: Inspirational quotes database
- **general_info.json**: General application information

#### 7. Utilities (`banditgui/utils/`)

- **get_data.py**: Data fetching and processing utilities
- **level_info.py**: Level information management
- **quotes.py**: Quote system management
- **extract_commands.py**: Command extraction utilities

### Frontend Architecture

#### JavaScript Components

- **bandit-app.js**: Main application logic and state management
- **bandit-terminal.js**: Terminal interface controller
- **xterm-bandit-terminal.js**: Custom XTerm.js integration
- **quote-manager.js**: Quote display and management

#### Styling

- **bandit-terminal.css**: Custom terminal styling
- **xterm-custom.css**: XTerm.js customizations
- **xterm.css**: Base XTerm.js styles

## Installation

### Prerequisites

- Python 3.8 or higher
- Node.js and npm (for frontend dependencies)
- Git (for version control)
- Internet connection (for SSH connections to Bandit servers)

### Quick Start

#### Option 1: Automated Installation (Recommended)

**Windows:**

```bash
# Run the batch installer
install.bat
```

**macOS/Linux:**

```bash
# Make the installer executable
chmod +x install.sh
./install.sh
```

**Python Installer (Cross-platform):**

```bash
python install.py
```

#### Option 2: Manual Installation

1. **Clone the Repository**

```bash
git clone https://github.com/therealfredp3d/making-banditgui.git
cd making-banditgui
```

2. **Create Virtual Environment**

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Python Dependencies**

```bash
pip install -r requirements.txt
```

4. **Install Frontend Dependencies**

```bash
npm install
```

5. **Run the Application**

```bash
python -m banditgui.app
# Or use the convenience script
./run.sh
```

### Docker Installation (Coming Soon)

```bash
docker build -t banditgui .
docker run -p 5000:5000 banditgui
```

## Usage Guide

### Getting Started

1. **Launch the Application**
   - Navigate to `http://localhost:5000` in your web browser
   - You'll see the main BanditGUI interface

2. **Connect to Bandit**
   - Select your desired Bandit level
   - Enter your SSH credentials (username: bandit0, password: bandit0 for level 0)
   - Click "Connect" to establish the SSH session

3. **Using the Terminal**
   - The terminal emulates a full SSH session
   - All standard Linux commands are available
   - Use the built-in hints system for guidance

### Key Features Usage

#### Level Navigation

- Use the level selector to jump between different Bandit levels
- Each level displays relevant information and hints
- Progress is automatically tracked

#### Chat System

- Access the chat panel for real-time communication
- Share hints and collaborate with other users
- Chat history is preserved across sessions

#### Command History

- Access previous commands using the up/down arrow keys
- Command history persists across sessions
- Search through command history with Ctrl+R

#### Hints and Tips

- Click the "Hints" button for level-specific guidance
- Access the command reference for common Linux commands
- View geek quotes for motivation

### Advanced Usage

#### Custom Configurations

- Modify `banditgui/config/settings.py` for custom settings
- Adjust terminal appearance in the CSS files
- Configure logging levels and output destinations

#### API Integration

- Use the RESTful API for programmatic access
- Integrate with other tools and systems
- Build custom frontends using the API

## API Documentation

### Base URL

```
http://localhost:5000/api
```

### Authentication

Currently, BanditGUI uses session-based authentication. API endpoints may require valid session cookies.

### Endpoints

#### SSH Management

```http
POST /api/ssh/connect
Content-Type: application/json

{
  "hostname": "bandit.labs.overthewire.org",
  "username": "bandit0",
  "password": "bandit0",
  "port": 2220
}
```

#### Terminal Operations

```http
POST /api/terminal/execute
Content-Type: application/json

{
  "command": "ls -la",
  "session_id": "session_123"
}
```

#### Level Information

```http
GET /api/levels/{level_id}
```

#### Chat Operations

```http
POST /api/chat/send
Content-Type: application/json

{
  "message": "Hello, world!",
  "room": "general"
}
```

#### Data Retrieval

```http
GET /api/data/commands
GET /api/data/quotes
GET /api/data/levels
```

### WebSocket Events

#### Terminal Events

- `terminal:output` - Receive terminal output
- `terminal:input` - Send terminal input
- `terminal:resize` - Handle terminal resize

#### Chat Events

- `chat:message` - Receive chat messages
- `chat:join` - User joins chat room
- `chat:leave` - User leaves chat room

## Development Guide

### Setting Up Development Environment

1. **Fork and Clone**

```bash
git clone https://github.com/yourusername/making-banditgui.git
cd making-banditgui
```

2. **Install Development Dependencies**

```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt  # If available
```

3. **Set Environment Variables**

```bash
export FLASK_ENV=development
export FLASK_DEBUG=1
```

### Code Structure Guidelines

#### Python Code Style

- Follow PEP 8 guidelines
- Use type hints where appropriate
- Write comprehensive docstrings
- Include unit tests for new features

#### JavaScript Code Style

- Use ES6+ features
- Follow consistent naming conventions
- Comment complex logic
- Use meaningful variable names

#### File Organization

```
banditgui/
├── module_name/
│   ├── __init__.py
│   ├── main_module.py
│   └── tests/
│       └── test_main_module.py
```

### Testing

#### Running Tests

```bash
# Run all tests
python -m pytest

# Run specific test file
python -m pytest banditgui/utils/test_level_info.py

# Run with coverage
python -m pytest --cov=banditgui
```

#### Writing Tests

```python
import unittest
from banditgui.utils.level_info import get_level_info

class TestLevelInfo(unittest.TestCase):
    def test_get_level_info(self):
        info = get_level_info(0)
        self.assertIsNotNone(info)
        self.assertIn('description', info)
```

### Contributing Workflow

1. **Create Feature Branch**

```bash
git checkout -b feature/awesome-feature
```

2. **Make Changes**
   - Write code following the style guidelines
   - Add tests for new functionality
   - Update documentation as needed

3. **Test Changes**

```bash
python -m pytest
```

4. **Submit Pull Request**
   - Provide clear description of changes
   - Include screenshots for UI changes
   - Reference related issues

## Configuration

### Application Settings (`banditgui/config/settings.py`)

```python
# Server Configuration
HOST = '127.0.0.1'
PORT = 5000
DEBUG = True

# SSH Configuration
SSH_TIMEOUT = 30
MAX_CONNECTIONS = 100

# Security Settings
SECRET_KEY = 'your-secret-key-here'
SESSION_TIMEOUT = 3600

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FILE = 'banditgui.log'
```

### Environment Variables

```bash
# Development
export FLASK_ENV=development
export FLASK_DEBUG=1

# Production
export FLASK_ENV=production
export BANDITGUI_SECRET_KEY=your-production-secret-key
export BANDITGUI_HOST=0.0.0.0
export BANDITGUI_PORT=80
```

### Logging Configuration

The application uses Python's built-in logging module with configurable levels:

- **DEBUG**: Detailed diagnostic information
- **INFO**: General operational messages
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failures
- **CRITICAL**: Critical errors that may cause the application to stop

## Troubleshooting

### Common Issues

#### Connection Issues

**Problem**: Cannot connect to Bandit servers
**Solution**:

- Check internet connectivity
- Verify SSH credentials
- Ensure port 2220 is not blocked by firewall

#### Terminal Display Issues

**Problem**: Terminal not displaying correctly
**Solution**:

- Clear browser cache
- Disable browser extensions
- Check JavaScript console for errors

#### Installation Problems

**Problem**: Dependencies fail to install
**Solution**:

- Update pip: `pip install --upgrade pip`
- Use virtual environment
- Install system dependencies (build tools)

### Debug Mode

Enable debug mode for detailed error information:

```bash
export FLASK_DEBUG=1
python -m banditgui.app
```

### Logging

Check application logs for detailed error information:

```bash
tail -f banditgui.log
```

### Getting Help

1. **Check Documentation**: Review this documentation thoroughly
2. **Search Issues**: Look through existing GitHub issues
3. **Create Issue**: Submit a detailed bug report with:
   - Operating system and version
   - Python version
   - Error messages and stack traces
   - Steps to reproduce the issue

## Contributing

We welcome contributions from the community! Here's how you can help:

### Ways to Contribute

1. **Code Contributions**
   - Bug fixes
   - New features
   - Performance improvements
   - Code refactoring

2. **Documentation**
   - Improve existing documentation
   - Add tutorials and examples
   - Translate documentation

3. **Testing**
   - Write unit tests
   - Perform integration testing
   - Report bugs and issues

4. **Design**
   - UI/UX improvements
   - Logo and branding
   - Icons and graphics

### Contribution Process

1. **Fork the Repository**
2. **Create a Feature Branch**
3. **Make Your Changes**
4. **Write Tests**
5. **Update Documentation**
6. **Submit a Pull Request**

### Code of Conduct

Please read and follow our Code of Conduct to ensure a welcoming environment for all contributors.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Third-party Licenses

- XTerm.js: MIT License
- Flask: BSD License
- Other dependencies as specified in requirements.txt

## Acknowledgments

- **OverTheWire Team**: For creating the amazing Bandit wargame
- **XTerm.js Team**: For the excellent terminal emulation library
- **Flask Community**: For the robust web framework
- **Contributors**: All the amazing people who have contributed to this project

---

*BanditGUI - Making cybersecurity education more accessible, one terminal at a time.*
