# System Context

## I am working on a software system with the following directory structure, architecture, and analyzed files

## Directory Structure

```
Remaking-BanditGUI
├── banditgui
│   ├── chat
│   │   ├── __init__.py
│   │   ├── chat_manager.py
│   │   └── routes.py
│   ├── config
│   │   ├── __init__.py
│   │   ├── llm_model.json
│   │   ├── logging.py
│   │   └── settings.py
│   ├── data
│   │   ├── all_data.json
│   │   ├── commands_data.json
│   │   ├── geek_quotes.json
│   │   ├── general_info.json
│   │   ├── levels_info.json
│   │   ├── README.md
│   │   └── tldr-pages-book.pdf
│   ├── ssh
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── ssh_manager.py
│   ├── static
│   │   ├── js
│   │   │   ├── bandit-app.js
│   │   │   ├── bandit-terminal.js
│   │   │   ├── quote-manager.js
│   │   │   ├── xterm-addon-fit.js
│   │   │   ├── xterm-addon-web-links.js
│   │   │   ├── xterm-bandit-terminal.js
│   │   │   └── xterm.js
│   │   ├── bandit-terminal.css
│   │   ├── xterm-custom.css
│   │   └── xterm.css
│   ├── templates
│   │   └── index.html
│   ├── terminal
│   │   ├── __init__.py
│   │   └── terminal_manager.py
│   ├── tests
│   │   ├── test_app.py
│   │   └── test_llm_models.py
│   ├── utils
│   │   ├── __init__.py
│   │   ├── decorators.py
│   │   ├── extract_commands.py
│   │   ├── gemini-models.py
│   │   ├── get_data.py
│   │   ├── level_info.py
│   │   ├── llm_utils.py
│   │   ├── quotes.py
│   │   └── test_level_info.py
│   ├── __init__.py
│   ├── app.py
│   └── exceptions.py
├── docs
│   ├── assets
│   │   ├── BanditGUI-Badge.jpg
│   │   ├── favicon.ico
│   │   ├── generated-icon.png
│   │   ├── head-pixel.png
│   │   ├── head-title.png
│   │   ├── need_a_hand.gif
│   │   ├── quote.md
│   │   ├── v0.1-Diagram-full.jpg
│   │   ├── v0.1-Diagram-min.jpg
│   │   ├── v0.2-screenshot.jpg
│   │   ├── v0.3-Min-Overview.jpg
│   │   ├── v0.3-Overview.jpg
│   │   ├── v0.3-screenshot.jpg
│   │   ├── v0.3-Sourcetrail.png
│   │   ├── v0.3.1-Ask-a-Pro.jpg
│   │   ├── v0.3.1-level0-completed.jpg
│   │   ├── v0.3.1-level0.jpg
│   │   ├── v0.3.1-main-screen.jpg
│   │   ├── v0.3.2-UI-Screenshot.png
│   │   └── v0.4-Ask-a-Pro.jpg
│   ├── dev_notes
│   │   ├── PR
│   │   │   └── PR-23
│   │   ├── v0.3--FINAL-Overview.md
│   │   ├── v0.3-FINAL-Review.md
│   │   ├── v0.3-ROADMAP.md
│   │   └── v0.3.1-context.md
│   ├── drawio
│   │   ├── v0.2-Detailled-Overview.drawio
│   │   ├── v0.3-Chat-Flow.drawio
│   │   ├── v0.3-Detailled-Overview.drawio
│   │   ├── v0.3-Min-Overview.drawio
│   │   ├── v0.3-SSH-Flow-Detailed .md
│   │   ├── v0.3-SSH-Flow-Detailed.drawio
│   │   ├── v0.3-SSH-Flow.drawio
│   │   ├── v0.3.1-Ask-a-Pro-LLM-Full.drawio
│   │   ├── v0.3.1-Ask-a-Pro-LLM-Full.jpg
│   │   └── v0.4.0-Ask-a-Pro-Overview.drawio
│   ├── notion
│   │   ├── notion_development_notes.md
│   │   ├── notion_features_overview.md
│   │   ├── notion_homepage.md
│   │   ├── notion_installation_guide.md
│   │   ├── notion_project_documentation.md
│   │   ├── notion_roadmap.md
│   │   └── notion_screenshots_media.md
│   ├── promo
│   │   ├── blog.md
│   │   ├── notion_update.md
│   │   ├── presentation.md
│   │   └── shorts.md
│   ├── banditgui-description.mp3
│   ├── Extra-Learning.md
│   ├── v0.1-Prototype.md
│   ├── v0.2-FINAL-Components-dev.md
│   ├── v0.2-FINAL-Overview.md
│   ├── v0.3-Chat-Flow.md
│   ├── v0.3-Min-Overview.md
│   ├── v0.3-Overview.md
│   ├── v0.3-SSH-Flow.md
│   ├── v0.3.1-Ask-A-Pro-LLM-Diagrams.md
│   ├── v0.3.1-Briefing-Document.md
│   ├── v0.4-Features.md
│   ├── v0.4-Project-Overview.md
│   └── v0.4.0-Context.md
├── llm_models
│   ├── generate_model_list.py
│   ├── model_list_gemini.json
│   ├── model_list_openrouter.json
│   ├── README.md
│   └── valid_model_list.json
├── static
│   ├── js
│   │   ├── xterm-addon-fit.js
│   │   ├── xterm-addon-web-links.js
│   │   └── xterm.js
│   └── xterm.css
├── CHANGELOG.md
├── icon.png
├── install.py
├── package-lock.json
├── package.json
├── README.md
├── requirements.txt
└── run.sh

```

## Mermaid Diagram

```mermaid
graph TD

    4474["Installer<br>Python Script"]
    4475["LLM Model List Generator<br>Python Script"]
    4479["User<br>External Actor"]
    subgraph 4465["External Systems"]
        4476["AI APIs<br>Gemini, OpenRouter, etc."]
        4477["Remote SSH Servers<br>SSH Protocol"]
        4478["Game Data Source<br>OverTheWire.org"]
    end
    subgraph 4466["BanditGUI Web Application<br>Flask, Python, JS"]
        4467["Flask Application<br>Python, Flask"]
        4468["Web Frontend<br>HTML, CSS, JS, Xterm.js"]
        4469["Chat Services<br>Python"]
        4470["SSH Services<br>Python, Paramiko"]
        4471["Terminal Manager<br>Python"]
        4472["Core Utilities<br>Python"]
        4473["Application Configuration<br>Python, JSON"]
        %% Edges at this level (grouped by source)
        4468["Web Frontend<br>HTML, CSS, JS, Xterm.js"] -->|Sends API requests to| 4467["Flask Application<br>Python, Flask"]
        4467["Flask Application<br>Python, Flask"] -->|Serves content to| 4468["Web Frontend<br>HTML, CSS, JS, Xterm.js"]
        4467["Flask Application<br>Python, Flask"] -->|Delegates to| 4469["Chat Services<br>Python"]
        4467["Flask Application<br>Python, Flask"] -->|Delegates to| 4470["SSH Services<br>Python, Paramiko"]
        4467["Flask Application<br>Python, Flask"] -->|Uses| 4471["Terminal Manager<br>Python"]
        4467["Flask Application<br>Python, Flask"] -->|Uses general utils from| 4472["Core Utilities<br>Python"]
        4467["Flask Application<br>Python, Flask"] -->|Reads settings from| 4473["Application Configuration<br>Python, JSON"]
        4469["Chat Services<br>Python"] -->|Uses LLM utils from| 4472["Core Utilities<br>Python"]
        4469["Chat Services<br>Python"] -->|Reads config from| 4473["Application Configuration<br>Python, JSON"]
        4470["SSH Services<br>Python, Paramiko"] -->|Uses utils from| 4472["Core Utilities<br>Python"]
        4470["SSH Services<br>Python, Paramiko"] -->|Reads config from| 4473["Application Configuration<br>Python, JSON"]
        4471["Terminal Manager<br>Python"] -->|Uses level info from| 4472["Core Utilities<br>Python"]
        4471["Terminal Manager<br>Python"] -->|Reads config from| 4473["Application Configuration<br>Python, JSON"]
        4472["Core Utilities<br>Python"] -->|Reads shared config from| 4473["Application Configuration<br>Python, JSON"]
    end
    %% Edges at this level (grouped by source)
    4474["Installer<br>Python Script"] -->|Sets up runtime for| 4467["Flask Application<br>Python, Flask"]
    4474["Installer<br>Python Script"] -->|Configures environment for| 4473["Application Configuration<br>Python, JSON"]
    4479["User<br>External Actor"] -->|Interacts with| 4468["Web Frontend<br>HTML, CSS, JS, Xterm.js"]
    4469["Chat Services<br>Python"] -->|Calls for AI completion| 4476["AI APIs<br>Gemini, OpenRouter, etc."]
    4470["SSH Services<br>Python, Paramiko"] -->|Connects to| 4477["Remote SSH Servers<br>SSH Protocol"]
    4472["Core Utilities<br>Python"] -->|Fetches data from| 4478["Game Data Source<br>OverTheWire.org"]
    4475["LLM Model List Generator<br>Python Script"] -->|Writes LLM config to| 4473["Application Configuration<br>Python, JSON"]
    4475["LLM Model List Generator<br>Python Script"] -->|Fetches model info via| 4476["AI APIs<br>Gemini, OpenRouter, etc."]

```

## Analyzed Files
