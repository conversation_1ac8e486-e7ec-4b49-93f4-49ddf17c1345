# BanditGUI

![BanditGUI Logo](https://github.com/TheRealFREDP3D/Making-BanditGUI/raw/main/docs/assets/v0.3-screenshot.jpg)

## Making Cybersecurity Learning Accessible

*A web-based interface for the popular OverTheWire Bandit wargame that brings terminal access, structured learning, and helpful hints to your browser.*

---

## The Challenge of Learning Cybersecurity

Learning cybersecurity can be intimidating. Between complex terminology, command-line interfaces, and the need for specialized environments, many beginners find themselves overwhelmed before they even start. The OverTheWire Bandit wargame is an excellent resource for beginners, but it still requires SSH access and command-line knowledge that can create barriers to entry.

That's where BanditGUI comes in.

---

## What is BanditGUI?

BanditGUI is an open-source web application that provides a user-friendly interface to the OverTheWire Bandit wargame. It combines a full-featured terminal emulator with a helpful chat assistant, allowing users to:

- Connect to the Bandit server directly from their browser
- Execute commands in a real SSH terminal
- Access level-specific information and hints
- Track their progress through the challenges
- Learn Linux commands and security concepts in a structured way

With BanditGUI, we're removing the barriers to entry for cybersecurity education, making it accessible to anyone with a web browser.

---

## Key Features

### 1. Full-Featured Terminal Experience

- Responsive and interactive terminal interface
- Support for ANSI color codes for better visual feedback
- Command history navigation with arrow keys
- Automatic terminal resizing
- Clickable URLs in terminal output

### 2. Real SSH Connections

- Establish actual SSH connections to the Bandit server
- Execute real Linux commands
- Experience authentic terminal interactions
- Learn in a realistic environment
- Receive immediate feedback on commands

### 3. Structured Learning Approach

- Level-specific goals and objectives
- Suggested commands with links to documentation
- Helpful reading materials and resources
- A structured approach to progressing through the challenges

### 4. Chat Interface

- Helpful hints for each level
- Responds to basic commands like 'help', 'info', and 'level'
- User-friendly way to access information
- Interactive learning experience

---

## Technical Implementation

BanditGUI is built with modern web technologies:

- **Backend**: Python with Flask for the web server
- **Frontend**: HTML, CSS, and JavaScript
- **Terminal**: xterm.js with FitAddon and WebLinksAddon
- **SSH**: Paramiko for secure SSH connections
- **Data**: JSON-based storage for level information

---

## Getting Started

Ready to try BanditGUI? Installation is now easier than ever with our automated installation scripts!

Visit our [Installation Guide](/Installation-Guide) for detailed instructions.

---

## Project Roadmap

We have exciting plans for future versions of BanditGUI. Check out our [Roadmap](/Roadmap) to see what's coming next.

---

## Contact

BanditGUI is an open-source project developed by Frederick Pellerin.

- Email: <EMAIL>
- GitHub: [TheRealFREDP3D](https://github.com/therealfredp3d)
- X: [@therealfredp3D](https://x.com/therealfredp3D)

---

## Links

- [GitHub Repository](https://github.com/TheRealFREDP3D/Making-BanditGUI)
- [Installation Guide](/Installation-Guide)
- [Features Overview](/Features-Overview)
- [Project Documentation](/Project-Documentation)
- [Development Notes](/Development-Notes)
- [Screenshots & Media](/Screenshots-Media)
