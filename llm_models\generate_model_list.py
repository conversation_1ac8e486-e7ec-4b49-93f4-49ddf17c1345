import json
import os

import google.generativeai as genai
import requests
from dotenv import load_dotenv

load_dotenv()

def generate_and_process_models():
    valid_models_file = "valid_model_list.json"
    gemini_models_output_file = os.path.join("llm_models", "model_list_gemini.json")
    openrouter_models_output_file = os.path.join("llm_models", "model_list_openrouter.json")

    final_consolidated_models = {"gemini": [], "openrouter": {}}

    # --- Fetch Gemini models ---
    gemini_all_models = []
    try:
        gemini_all_models = [m.name for m in genai.list_models()]
        print(f"Fetched {len(gemini_all_models)} raw Gemini models.")
    except Exception as e:
        print(f"Error fetching Gemini models: {e}")

    # Filter Gemini models
    gemini_filtered_models = [
        m for m in gemini_all_models
        if "latest" in m.lower() or "preview" in m.lower() or "thinking" in m.lower()
    ]
    print(f"Filtered to {len(gemini_filtered_models)} Gemini models.")

    # Save filtered Gemini models to a separate file
    with open(gemini_models_output_file, 'w', encoding='utf-8') as f:
        json.dump(gemini_filtered_models, f, indent=4)
    print(f"Saved filtered Gemini models to {gemini_models_output_file}.")
    
    final_consolidated_models["gemini"] = gemini_filtered_models

    # --- Fetch OpenRouter models ---
    openrouter_all_models = []
    openrouter_api_url = "https://openrouter.ai/api/v1/models"
    headers = {}
    try:
        response = requests.get(openrouter_api_url, headers=headers)
        response.raise_for_status()
        openrouter_data = response.json()
        
        for model in openrouter_data.get('data', []):
            openrouter_all_models.append({
                "id": model.get('id'),
                "name": model.get('name'),
                "description": model.get('description'),
                "context_length": model.get('context_length'),
                "tokenizer": model.get('architecture', {}).get('tokenizer'),
                "instruct_type": model.get('architecture', {}).get('instruct_type'),
                "pricing": model.get('pricing', {})
            })
        print(f"Fetched {len(openrouter_all_models)} raw OpenRouter models.")
    except requests.exceptions.RequestException as e:
        print(f"Error fetching OpenRouter models: {e}")
    except json.JSONDecodeError as e:
        print(f"Error decoding OpenRouter API response: {e}")

    # Filter OpenRouter models for 'free' in their name
    openrouter_filtered_models = [
        model for model in openrouter_all_models 
        if model.get("name") and "free" in model["name"].lower()
    ]
    print(f"Filtered to {len(openrouter_filtered_models)} 'free' OpenRouter models.")

    # Save filtered OpenRouter models to a separate file
    with open(openrouter_models_output_file, 'w', encoding='utf-8') as f:
        json.dump(openrouter_filtered_models, f, indent=4)
    print(f"Saved filtered OpenRouter models to {openrouter_models_output_file}.")

    final_consolidated_models["openrouter"] = {m["id"]: m for m in openrouter_filtered_models}

    # Write updated consolidated data back to valid_model_list.json
    with open(valid_models_file, 'w', encoding='utf-8') as f:
        json.dump(final_consolidated_models, f, indent=4)
    print(f"\nUpdated {valid_models_file} with {len(final_consolidated_models['gemini'])} Gemini models and {len(final_consolidated_models['openrouter'])} OpenRouter models.")

if __name__ == "__main__":
    generate_and_process_models()
