"""Main Flask application for BanditGUI.

This module initializes the Flask application and defines the API routes.
"""

import os
import sys

from flask import Flask, jsonify, render_template
from werkzeug.exceptions import HTTPException

from banditgui.chat.chat_manager import ChatManager
from banditgui.chat.routes import chat_bp  # Import the Chat Blueprint
from banditgui.config.logging import get_logger, setup_logging

# Initialize configuration and logging
from banditgui.config.settings import config
from banditgui.data.routes import data_bp  # Import the Data Blueprint
from banditgui.general.routes import general_bp  # Import the General Blueprint
from banditgui.ssh.routes import ssh_bp  # Import the SSH Blueprint
from banditgui.ssh.ssh_manager import SSHManager
from banditgui.terminal.terminal_manager import TerminalManager

# Set up logging
setup_logging(log_level=os.getenv('LOG_LEVEL', 'INFO'))
logger = get_logger('app')

# Initialize Flask app
app = Flask(__name__, static_folder='static', static_url_path='/static')

# Initialize managers
ssh_manager = SSHManager()
terminal_manager = TerminalManager(ssh_manager=ssh_manager)
chat_manager = ChatManager()

# Register blueprints and pass managers via app config
app.config['ssh_manager'] = ssh_manager
app.config['terminal_manager'] = terminal_manager # Needed by ssh_bp for current_level
app.config['chat_manager'] = chat_manager
app.register_blueprint(ssh_bp)
app.register_blueprint(chat_bp)
app.register_blueprint(data_bp)
app.register_blueprint(general_bp, url_prefix='/general')

@app.route('/')
def index():
    """Serve the main application page."""
    return render_template('index.html')

@app.errorhandler(HTTPException)
def handle_http_exception(e):
    """Return JSON instead of HTML for HTTP errors."""
    logger.error(f"HTTP Exception: {e.code} - {e.description}", exc_info=True)
    return jsonify({"status": "error", "message": e.description}), e.code

logger.info("BanditGUI application initialized")


def main():
    """Run the Flask application."""
    # Validate configuration
    validation_error = config.validate()
    if validation_error:
        logger.error(f"Configuration error: {validation_error}")
        sys.exit(1)

    # Run the Flask app
    logger.info(f"Starting Flask app on {config.host}:{config.port}")
    app.run(debug=config.debug, host=config.host, port=config.port)


if __name__ == "__main__":
    main()
