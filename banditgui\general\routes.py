"""General routes for BanditGUI.

This module defines general API routes for the Flask application.
"""

import os

from flask import Blueprint, current_app, jsonify, render_template, send_from_directory

from banditgui.config.logging import get_logger
from banditgui.utils.quotes import quote_manager

logger = get_logger('general.routes')

general_bp = Blueprint('general', __name__)


@general_bp.route("/")
def home():
    """Render the home page."""
    logger.debug("Rendering home page")
    # Pass preferred LLM configuration to the template
    preferred_llm_provider = os.getenv("PREFERRED_LLM_PROVIDER", "").split('/')[0] # Get just the provider
    preferred_llm_model = os.getenv("PREFERRED_LLM_MODEL", "").split('/')[-1] # Get just the model name

    return render_template("index.html", 
                           preferred_llm_provider=preferred_llm_provider, 
                           preferred_llm_model=preferred_llm_model)


@general_bp.route('/static/js/<path:filename>')
def serve_js(filename):
    """Serve JavaScript files."""
    logger.debug(f"Serving JS file: {filename}")
    return send_from_directory('static/js', filename)


@general_bp.route('/config/<path:filename>')
def serve_config_json(filename):
    logger.debug(f"Serving config JSON file: {filename}")
    # Ensure the path is safe and only serves expected files
    if filename == "llm_model.json":
        # 'config' is relative to app.root_path, which is 'banditgui/'
        return send_from_directory('config', filename, mimetype='application/json')
    else:
        return jsonify({"status": "error", "message": "File not found"}), 404


@general_bp.route('/quotes/random', methods=['GET'])
def random_quote():
    """Get a random inspirational quote."""
    logger.debug("Fetching random quote")
    quote = quote_manager.get_random_quote()
    return jsonify({'quote': quote})


@general_bp.route('/quotes/welcome', methods=['GET'])
def welcome_quotes():
    """
    Get welcome quotes for the terminal.
    """
    logger.debug("Fetching welcome quotes")
    quotes = quote_manager.get_terminal_welcome_quotes(count=3)
    return jsonify({'status': 'success', 'quotes': quotes})


@general_bp.route('/server-status', methods=['GET'])
def server_status():
    """
    Check the status of the SSH server connection.
    """
    logger.debug("Checking server status")
    ssh_manager = current_app.config.get('ssh_manager')
    if ssh_manager is None:
        logger.error("SSHManager not initialized in blueprint.")
        return jsonify({"status": "error", "message": "Server component not ready."}), 500
    is_connected = ssh_manager.client is not None
    status = 'online' if is_connected else 'offline'
    return jsonify({"status": "success", "serverStatus": {"status": status}})
