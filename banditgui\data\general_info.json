{"general": "Bandit\nThe Bandit wargame is aimed at absolute beginners. It will teach the\nbasics needed to be able to play other wargames.\nIf you notice\nsomething essential is missing or have ideas for new levels, please let\nus know!\nNote for beginners\nThis game, like most other games, is organised in levels. You start at\nLevel 0 and try to “beat” or “finish” it. Finishing a level results in\ninformation on how to start the next level. The pages on this website\nfor “Level <X>” contain information on how to start level X from the\nprevious level. E.g. The page for\nLevel 1\nhas information on how to\ngain access from\nLevel 0\nto\nLevel 1\n. All levels in this game\nhave a page on this website, and they are all linked to from the\nsidemenu on the left of this page.\nYou will encounter many situations in which you have no idea what you\nare supposed to do.\nDon’t panic! Don’t give up!\nThe purpose of this\ngame is for you to learn the basics. Part of learning the basics, is\nreading a lot of new information. If you’ve never used the command line\nbefore, a good first read is this\nintroduction to user commands\n.\nThere are several things you can try when you are unsure how to\ncontinue:\nFirst, if you know a command, but don’t know how to use it, try the\nmanual\n(\nman page\n) by entering\nman <command>\n.\nFor example,\nman ls\nto learn about the “ls” command.\nThe “man” command also has a manual, try it!\nWhen using\nman\n, press\nq\nto quit\n(you can also use\n/\nand\nn\nand\nN\nto search).\nSecond, if there is no man page, the command might be a\nshell\nbuilt-in\n. In that case use the “\nhelp <X>\n” command. E.g. help\ncd\nAlso, your favorite\nsearch-engine\nis your friend. Learn how to\nuse it! I recommend\nGoogle\n.\nLastly, if you are still stuck, you can\njoin us via chat\nYou’re ready to start! Begin with\nLevel 0\n, linked at the left of\nthis page. Good luck!\nNote for VMs:\nYou may fail to connect to overthewire.org via SSH with a “\nbroken pipe error\n” when the network adapter for the VM is configured to use NAT mode. Adding the setting\nIPQoS throughput\nto\n/etc/ssh/ssh_config\nshould resolve the issue. If this does not solve your issue, the only option then is to change the adapter to Bridged mode."}