========== PROJECT MAP ==========

Directory structure:
├── banditgui/
│   ├── chat/
│   │   ├── __init__.py
│   │   ├── chat_manager.py
│   │   └── routes.py
│   ├── config/
│   │   ├── __init__.py
│   │   ├── logging.py
│   │   └── settings.py
│   ├── data/
│   │   └── routes.py
│   ├── general/
│   │   └── routes.py
│   ├── ssh/
│   │   ├── __init__.py
│   │   ├── routes.py
│   │   └── ssh_manager.py
│   ├── static/
│   │   └── js/
│   │       ├── bandit-app.js
│   │       ├── bandit-terminal.js
│   │       ├── quote-manager.js
│   │       ├── xterm-addon-fit.js
│   │       ├── xterm-addon-web-links.js
│   │       ├── xterm-bandit-terminal.js
│   │       └── xterm.js
│   ├── templates/
│   │   └── index.html
│   ├── terminal/
│   │   ├── __init__.py
│   │   └── terminal_manager.py
│   ├── tests/
│   │   ├── test_app.py
│   │   └── test_llm_models.py
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── decorators.py
│   │   ├── extract_commands.py
│   │   ├── get_data.py
│   │   ├── level_info.py
│   │   ├── llm_utils.py
│   │   ├── quotes.py
│   │   └── test_level_info.py
│   ├── __init__.py
│   ├── app.py
│   └── exceptions.py
├── llm_models/
│   └── generate_model_list.py
├── static/
│   └── js/
│       ├── xterm-addon-fit.js
│       ├── xterm-addon-web-links.js
│       └── xterm.js
└── install.py


========== STATISTICS ==========

Combined Files Size: -
Character Count: -
Estimated Tokens: -
Selected Files: 39
