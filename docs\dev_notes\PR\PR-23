# BanditGUI - Pull Request 23 - Ask-a-Pro 

[Sourcery Review](https://github.com/TheRealFREDP3D/Making-BanditGUI/pull/23#issuecomment-2933489969)

<!-- Generated by sourcery-ai[bot]: start review_guide -->

## Reviewer's Guide

This PR primarily updates the documentation: it bumps the version, enriches the chat interface section with LLM details, refines the technical implementation and roadmap, adds contribution guidelines, and fixes an external script’s integrity hash.

#### Sequence Diagram: LLM-Powered Hint Generation

```mermaid
sequenceDiagram
    title Sequence Diagram: LLM-Powered Hint Generation
    actor User
    participant B as "BanditGUI"
    participant L as "Google Gemini LLM"

    User->>B: Asks for hint / help via Chat Interface
    activate B
    B->>L: Request intelligent hint (user query, level context)
    activate L
    L-->>B: Provides context-aware hint
    deactivate L
    B-->>User: Displays enhanced hint in Chat Interface
    deactivate B
```

### File-Level Changes

| Change | Details | Files |
| ------ | ------- | ----- |
| Bump project version | <ul><li>Updated version number in README title</li></ul> | `README.md` |
| Enhance chat interface with LLM details | <ul><li>Renamed section to LLM-Powered Chat Interface</li><li>Rewrote bullets to highlight context-aware hints, enhanced commands, and dynamic engagement</li><li>Referenced litellm and LLM providers in the description</li></ul> | `README.md` |
| Refine technical implementation and roadmap | <ul><li>Added LLM Integration entry under Technical Implementation</li><li>Removed Advanced Terminal Features from future plans and renumbered Accessibility Improvements</li></ul> | `README.md` |
| Add community contribution guidelines | <ul><li>Introduced a How to Contribute section</li><li>Outlined steps for forking, branching, committing, testing, and opening a PR</li></ul> | `README.md` |
| Update external dependency integrity hash | <ul><li>Updated integrity attribute for marked.min.js CDN script</li></ul> | `banditgui/templates/index.html` |

---

<details>
<summary>Tips and commands</summary>

#### Interacting with Sourcery

- **Trigger a new review:** Comment `@sourcery-ai review` on the pull request.
- **Continue discussions:** Reply directly to Sourcery's review comments.
- **Generate a GitHub issue from a review comment:** Ask Sourcery to create an
  issue from a review comment by replying to it. You can also reply to a
  review comment with `@sourcery-ai issue` to create an issue from it.
- **Generate a pull request title:** Write `@sourcery-ai` anywhere in the pull
  request title to generate a title at any time. You can also comment
  `@sourcery-ai title` on the pull request to (re-)generate the title at any time.
- **Generate a pull request summary:** Write `@sourcery-ai summary` anywhere in
  the pull request body to generate a PR summary at any time exactly where you
  want it. You can also comment `@sourcery-ai summary` on the pull request to
  (re-)generate the summary at any time.
- **Generate reviewer's guide:** Comment `@sourcery-ai guide` on the pull
  request to (re-)generate the reviewer's guide at any time.
- **Resolve all Sourcery comments:** Comment `@sourcery-ai resolve` on the
  pull request to resolve all Sourcery comments. Useful if you've already
  addressed all the comments and don't want to see them anymore.
- **Dismiss all Sourcery reviews:** Comment `@sourcery-ai dismiss` on the pull
  request to dismiss all existing Sourcery reviews. Especially useful if you
  want to start fresh with a new review - don't forget to comment
  `@sourcery-ai review` to trigger a new review!

#### Customizing Your Experience

Access your [dashboard](https://app.sourcery.ai) to:
- Enable or disable review features such as the Sourcery-generated pull request
  summary, the reviewer's guide, and others.
- Change the review language.
- Add, remove or edit custom review instructions.
- Adjust other review settings.

#### Getting Help

- [Contact our support team](mailto:<EMAIL>) for questions or feedback.
- Visit our [documentation](https://docs.sourcery.ai) for detailed guides and information.
- Keep in touch with the Sourcery team by following us on [X/Twitter](https://x.com/SourceryAI), [LinkedIn](https://www.linkedin.com/company/sourcery-ai/) or [GitHub](https://github.com/sourcery-ai).

</details>

<!-- Generated by sourcery-ai[bot]: end review_guide -->