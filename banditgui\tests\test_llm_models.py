import json
import os

import pytest

# Define the path to the valid_model_list.json file
# Assuming the test is run from the project root or a location where llm_models is accessible
MODELS_FILE_PATH = os.path.join("valid_model_list.json")

def test_valid_model_list_exists():
    """
    Test that valid_model_list.json exists.
    """
    assert os.path.exists(MODELS_FILE_PATH), f"File not found: {MODELS_FILE_PATH}"

def test_valid_model_list_is_valid_json():
    """
    Test that valid_model_list.json is a valid JSON file.
    """
    with open(MODELS_FILE_PATH, 'r', encoding='utf-8') as f:
        try:
            json.load(f)
        except json.JSONDecodeError:
            pytest.fail(f"Invalid JSON in file: {MODELS_FILE_PATH}")

def test_gemini_models_are_present():
    """
    Test that Gemini models are present and the list is not empty.
    """
    with open(MODELS_FILE_PATH, 'r', encoding='utf-8') as f:
        data = json.load(f)
    assert "gemini" in data, "Gemini models key not found in valid_model_list.json"
    assert isinstance(data["gemini"], list), "Gemini models should be a list"
    assert len(data["gemini"]) > 0, "Gemini models list is empty"

def test_openrouter_models_are_present():
    """
    Test that OpenRouter models are present and the dictionary is not empty.
    """
    with open(MODELS_FILE_PATH, 'r', encoding='utf-8') as f:
        data = json.load(f)
    assert "openrouter" in data, "OpenRouter models key not found in valid_model_list.json"
    assert isinstance(data["openrouter"], dict), "OpenRouter models should be a dictionary"
    assert len(data["openrouter"]) > 0, "OpenRouter models dictionary is empty"
