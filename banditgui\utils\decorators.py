"""
Decorators for Flask application to handle common concerns.
"""

from functools import wraps

from flask import jsonify

from banditgui.config.logging import get_logger

logger = get_logger('decorators')

def api_error_handler(f):
    """
    A decorator to wrap API routes and handle common exceptions, returning JSON error responses.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"An unexpected error occurred in {f.__name__}: {str(e)}", exc_info=True)
            return jsonify({"status": "error", "message": "An internal server error occurred."}), 500
    return decorated_function 