## Version v0.5.0-Code-Refactoring-and-Modularization

### Added
- Created `banditgui/general/routes.py` to house general application routes, improving modularity.
- Added `banditgui/.env.example` to provide clear guidance for environment variable configuration.

### Changed
- Refactored `banditgui/app.py` to use Flask Blueprints for chat, data, ssh, and general routes, significantly improving code organization and maintainability.
- Moved `_construct_ask_a_pro_prompt` method from `banditgui/chat/chat_manager.py` to `banditgui/utils/llm_utils.py` for better centralization of AI-related utilities.
- Adjusted import statements in `banditgui/data/routes.py` and `banditgui/chat/chat_manager.py` for consistent styling.
- Eliminated redundant module-level functions in `banditgui/utils/level_info.py` and `banditgui/utils/quotes.py`, enforcing the use of singleton instances for managing level information and quotes.
- Updated `banditgui/utils/test_level_info.py` to use the `level_info_instance` singleton directly.
- Consolidated configuration loading and validation within `banditgui/config/settings.py`.
- Corrected argument order for `chat_manager.add_message` and removed unused `model_parts` references in `banditgui/chat/routes.py`.
- Removed auto-connection logic from `execute_command` in `banditgui/ssh/ssh_manager.py`.
- Fixed success check in `connect_command` and removed auto-connection logic from `execute_command` and `ssh_command` in `banditgui/terminal/terminal_manager.py`.
- Standardized `DATA_DIR` and `OUTPUT_DIR` paths in `banditgui/utils/extract_commands.py` and `banditgui/utils/get_data.py`.
- Corrected the `server_status` route in `banditgui/general/routes.py`.
- Moved `banditgui.utils` imports to the top of `banditgui/terminal/terminal_manager.py` to resolve potential circular import issues.
- Refined SSH error handling in `banditgui/ssh/ssh_manager.py` to specifically catch `paramiko.AuthenticationException`.
- Standardized logger names in `banditgui/chat/routes.py`, `banditgui/ssh/routes.py`, and `banditgui/data/routes.py` to follow the `module.submodule` naming convention.

### Removed
- Old route definitions from `banditgui/app.py` that were migrated to the new `general_bp` blueprint.
- Redundant logging call in `banditgui/chat/chat_manager.py`.
- `banditgui/utils/gemini-models.py` as it was identified as dead code.

## Version v0.4.2-Ask-a-Pro-Branding-Update

### Changed
- Renamed "Mentor" to "Ask-a-Pro" across the application, including text in `banditgui/app.py` and `banditgui/static/js/bandit-app.js`.
- Updated CSS class from `mentor-message` to `ask-a-pro-message` in `banditgui/static/bandit-terminal.css` and adjusted its colors for consistency.
- Renamed JavaScript function `addMentorMessage` to `addAskAProMessage` and updated all its calls and related labels in `banditgui/static/js/bandit-app.js`.

## Version v0.4.1-LLM-Models-Update

### Changed
- Modified `/llm-models` endpoint in `banditgui/app.py` to read model data from `llm_models/valid_model_list.json` and adapt the JSON structure for the frontend.
- Modified `populateLlmDropdown` function in `banditgui/static/js/bandit-app.js` to correctly retrieve preferred LLM values from data attributes and ensure proper selection of the first available model if no preferred model is found.

## Version v0.4.0-Ask-a-Pro-Improvements

### Added
- New API endpoint `/llm-models` in `banditgui/app.py` to filter LLM models based on available API keys.

### Changed
- Modified `home` route in `banditgui/app.py` to pass `PREFERRED_LLM_PROVIDER` and `PREFERRED_LLM_MODEL` to `index.html`.
- Updated `banditgui/templates/index.html` to include `data-preferred-provider` and `data-preferred-model` attributes in the LLM selection dropdown.
- Modified `populateLlmDropdown` function in `banditgui/static/js/bandit-app.js` to fetch models from `/llm-models`, clear existing options, add a default placeholder, and pre-select the preferred LLM model. 