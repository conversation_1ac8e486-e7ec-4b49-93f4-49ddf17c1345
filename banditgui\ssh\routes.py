"""
SSH-related routes for BanditGUI.
"""

from flask import Blueprint, jsonify

from banditgui.config.logging import get_logger
from banditgui.exceptions import SSHConnectionError, SSHError
from banditgui.utils.decorators import api_error_handler

# Initialize logger for this blueprint
ssh_bp_logger = get_logger('ssh.routes')

# Create a Blueprint
ssh_bp = Blueprint('ssh', __name__, url_prefix='/ssh')

# This will be set by the main app when the blueprint is registered
ssh_manager = None
terminal_manager = None

@ssh_bp.record_once
def record_managers(state):
    """
    Callback to record manager instances from the main app.
    """
    global ssh_manager, terminal_manager
    if 'ssh_manager' in state.app.config:
        ssh_manager = state.app.config['ssh_manager']
    if 'terminal_manager' in state.app.config:
        terminal_manager = state.app.config['terminal_manager']
    ssh_bp_logger.debug("SSH Blueprint managers recorded.")

@ssh_bp.route('/server-status', methods=['GET'])
@api_error_handler
def server_status():
    """Check the status of the SSH server."""
    if ssh_manager is None:
        ssh_bp_logger.error("SSHManager not initialized in blueprint.")
        return jsonify({"status": "error", "message": "Server component not ready."}), 500

    ssh_bp_logger.info("Checking server status")
    status = ssh_manager.check_server_status()
    ssh_bp_logger.info(f"Server status: {status['status']}")
    return jsonify({'status': 'success', 'serverStatus': status})


@ssh_bp.route('/connect', methods=['POST'])
@api_error_handler
def connect():
    """Connect to the SSH server."""
    if ssh_manager is None or terminal_manager is None:
        ssh_bp_logger.error("Managers not initialized in blueprint for connect.")
        return jsonify({"status": "error", "message": "Server components not ready."}), 500

    ssh_bp_logger.info("Received connect request")
    try:
        ssh_manager.connect()
        ssh_bp_logger.info("SSH connection successful")
        # Set the current level to 0 (initial level)
        terminal_manager.current_level = 0
        return jsonify({
            'status': 'success',
            'message': 'Connected to SSH server',
            'currentLevel': terminal_manager.current_level
        })
    except SSHConnectionError as e:
        ssh_bp_logger.error(f"SSH connection failed: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500
    except SSHError as e:
        ssh_bp_logger.error(f"General SSH error during connection: {e}")
        return jsonify({'status': 'error', 'message': "A general SSH error occurred."}), 500


@ssh_bp.route('/disconnect', methods=['POST'])
@api_error_handler
def disconnect():
    """
    Disconnect from the SSH server.
    Note: This route is part of the /ssh blueprint, but the frontend still calls /disconnect.
    Adjusting frontend to call /ssh/disconnect is a future improvement.
    For now, handling this gracefully and logging a warning.
    """
    if ssh_manager is None or terminal_manager is None:
        ssh_bp_logger.error("Managers not initialized in blueprint for disconnect.")
        return jsonify({"status": "error", "message": "Server components not ready."}), 500

    ssh_bp_logger.info("Received disconnect request")
    ssh_manager.close()
    terminal_manager.ssh_connected = False
    terminal_manager.current_level = None
    ssh_bp_logger.info("SSH connection closed")
    return jsonify({
        'status': 'success',
        'message': 'Disconnected from SSH server'
    }) 