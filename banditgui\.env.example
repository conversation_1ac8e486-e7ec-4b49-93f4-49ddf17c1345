# Flask Settings
DEBUG=True
HOST=127.0.0.1
PORT=5000

# Logging Settings
LOG_LEVEL=INFO

# SSH Settings
SSH_HOST=bandit.labs.overthewire.org
SSH_PORT=2220
SSH_USERNAME=bandit0
SSH_PASSWORD=bandit0

# LLM Settings (for Ask-a-Pro feature)
# Choose one of the following providers and models. You may need to install additional libraries.
# For OpenAI, install with `pip install openai`
# For Groq, install with `pip install groq`
# For Ollama, ensure Ollama is running locally and model is pulled (e.g., `ollama pull llama3`)

# Example for OpenAI:
# PREFERRED_LLM_PROVIDER=openai
# PREFERRED_LLM_MODEL=gpt-4o-mini
# OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE

# Example for Groq:
# PREFERRED_LLM_PROVIDER=groq
# PREFERRED_LLM_MODEL=llama3-8b-8192
# GROQ_API_KEY=YOUR_GROQ_API_KEY_HERE

# Example for Ollama (local):
# PREFERRED_LLM_PROVIDER=ollama
# PREFERRED_LLM_MODEL=llama3
# OLLAMA_BASE_URL=http://localhost:11434 # Only if your Ollama instance is not on default port 