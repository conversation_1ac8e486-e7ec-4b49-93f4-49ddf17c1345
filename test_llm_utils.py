#!/usr/bin/env python3
"""
Test script for the new dynamic LLM model list generation.
"""

import os
import sys
import json

# Add the banditgui directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'banditgui'))

from banditgui.utils.llm_utils import generate_dynamic_model_list

def test_dynamic_model_list():
    """Test the dynamic model list generation."""
    print("Testing dynamic model list generation...")
    
    # Test with current .env configuration (should have placeholder keys)
    model_list = generate_dynamic_model_list()
    
    print(f"\nGenerated model list:")
    print(json.dumps(model_list, indent=2))
    
    # Verify structure
    expected_providers = ['openai', 'groq', 'github', 'gemini', 'openrouter']
    
    print(f"\nVerifying structure:")
    for provider in expected_providers:
        if provider in model_list:
            print(f"✓ {provider}: {len(model_list[provider])} models")
        else:
            print(f"✗ {provider}: missing")
    
    # Since all keys are placeholders, all lists should be empty
    all_empty = all(len(models) == 0 for models in model_list.values())
    if all_empty:
        print("\n✓ All model lists are empty (expected with placeholder API keys)")
    else:
        print("\n⚠ Some model lists are not empty (unexpected with placeholder API keys)")
        for provider, models in model_list.items():
            if models:
                print(f"  {provider}: {len(models)} models")

if __name__ == "__main__":
    test_dynamic_model_list()
