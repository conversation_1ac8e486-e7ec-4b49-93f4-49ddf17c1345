"""
Data-related routes for BanditGUI.
"""


from flask import Blueprint, jsonify, request

from banditgui.config.logging import get_logger
from banditgui.utils.decorators import api_error_handler
from banditgui.utils.level_info import get_level_info

# Initialize logger for this blueprint
data_bp_logger = get_logger('data.routes')

# Create a Blueprint
data_bp = Blueprint('data', __name__, url_prefix='/data')

# This will be set by the main app when the blueprint is registered
# No managers are directly needed for current level info based on current implementation, but can be added if needed.

@data_bp.route('/level-info', methods=['POST'])
@api_error_handler
def level_info():
    """Get information about a specific level."""
    level = request.json.get('level', 0)
    data_bp_logger.info(f"Requested level info for level {level}")

    level_data = get_level_info(level)

    if level_data:
        data_bp_logger.debug(f"Found level info for level {level}")
        return jsonify({'status': 'success', 'levelInfo': level_data})
    else:
        data_bp_logger.warning(f"Level info not found for level {level}")
        return jsonify({
            'status': 'error',
            'message': f'Level {level} information not found'
        }), 404 # Return 404 for not found


@data_bp.route('/llm-models', methods=['GET'])
@api_error_handler
def get_llm_models():
    """Get the list of available LLM models based on configured API keys."""
    data_bp_logger.info("Requested LLM models list")
    try:
        from banditgui.utils.llm_utils import generate_dynamic_model_list
        llm_models = generate_dynamic_model_list()
        return jsonify(llm_models)
    except Exception as e:
        data_bp_logger.error(f"Error generating LLM models list: {e}")
        return jsonify({"status": "error", "message": "Error generating LLM models list."}), 500
