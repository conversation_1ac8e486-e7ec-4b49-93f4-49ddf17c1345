# v0.5.0 - BanditGUI: Making Cybersecurity Learning Accessible

![Screenshot](docs/assets/v0.3.2-UI-Screenshot.png)

*A web-based interface for the popular OverTheWire Bandit wargame that brings terminal access, structured learning, and helpful hints to your browser.*

## The Challenge of Learning Cybersecurity

Learning cybersecurity can be intimidating. Between complex terminology, command-line interfaces, and the need for specialized environments, many beginners find themselves overwhelmed before they even start. The OverTheWire Bandit wargame is an excellent resource for beginners, but it still requires SSH access and command-line knowledge that can create barriers to entry.

That's where BanditGUI comes in.

## What is BanditGUI?

BanditGUI is an open-source web application that provides a user-friendly interface to the OverTheWire Bandit wargame. It combines a full-featured terminal emulator with a helpful chat assistant, allowing users to:

- Connect to the Bandit server directly from their browser
- Execute commands in a real SSH terminal
- Access level-specific information and hints
- Track their progress through the challenges
- Learn Linux commands and security concepts in a structured way

With BanditGUI, we're removing the barriers to entry for cybersecurity education, making it accessible to anyone with a web browser.

## Key Features in v0.5

The v0.5 release of BanditGUI brings several significant improvements, focusing on a more robust, maintainable, and user-friendly architecture:

### 1. Enhanced Modular Architecture

The application has been completely refactored to emphasize modularity and separation of concerns:

- **Flask Blueprints**: API routes are now organized into Flask Blueprints (Chat, Data, SSH, and General), significantly improving code organization and making the application easier to scale and manage.
- **Centralized Utilities**: AI-related prompt construction logic has been moved to a dedicated utility module, ensuring related functionalities are grouped logically.
- **Consistent Imports**: Import statements across the codebase have been standardized for improved readability and maintainability.
- **Removed Dead Code**: Identified and removed unused files like `gemini-models.py`, reducing project clutter and improving maintainability.

### 2. Singleton Pattern for Core Managers

Key managers and utility classes now enforce the singleton pattern, ensuring efficient resource management and consistent state:

- **Level Information**: Access to Bandit level data is managed through a single `LevelInfo` instance, which handles caching and data retrieval.
- **Quote Management**: Geek pop culture quotes are loaded and managed by a single `QuoteManager` instance, providing a centralized way to access random and formatted quotes.
- **Real SSH Connections**: Now establishes explicit SSH connections to the Bandit server, improving control and predictability of connection states.
- **Refined Error Handling**: Improved error handling for SSH connections, providing more specific feedback for authentication and general SSH errors.

### 3. Streamlined Configuration Management

Configuration is now more transparent and user-friendly:

- **`.env.example` File**: A new example environment file provides clear guidance for setting up Flask, logging, SSH, and LLM (Large Language Model) settings.
- **Centralized Validation**: Configuration loading and validation are consolidated within a dedicated settings module, ensuring application stability and proper setup.

### 4. Full-Featured Terminal Experience

We've implemented xterm.js, a powerful terminal emulator for the web, providing:

- A responsive and interactive terminal interface
- Support for ANSI color codes for better visual feedback
- Command history navigation with arrow keys
- Automatic terminal resizing with the FitAddon
- Clickable URLs in terminal output with the WebLinksAddon

### 5. Real SSH Connections

BanditGUI establishes actual SSH connections to the Bandit server, allowing users to:

- Execute real Linux commands
- Experience authentic terminal interactions
- Learn in a realistic environment
- Receive immediate feedback on their commands

#### SSH Connection Flow

![SSH Connection Flow](./docs/assets/v0.3-SSH-Flow.png)

*The diagram shows how user commands flow through the application components to the SSH server and back.*

The SSH implementation uses Paramiko to establish secure connections with password authentication. Commands entered in the terminal are sent to the SSH server via the SSHManager, with comprehensive error handling and logging throughout the process.

### 6. Level Information System

Users can access detailed information about each Bandit level:

- Level-specific goals and objectives
- Suggested commands with links to documentation
- Helpful reading materials and resources
- A structured approach to progressing through the challenges

### 7. LLM-Powered Chat Interface

The application includes an advanced chat interface powered by Large Language Models (LLMs), using `litellm` to support various API providers and LLM.

- Provides intelligent, context-aware hints for each level
- Responds to basic commands like 'help', 'info', and 'level' with enhanced understanding
- Offers a user-friendly and interactive way to access information and guidance
- Creates a more dynamic and engaging learning experience

## Technical Implementation

BanditGUI is built with modern web technologies:

- **Backend**: Python with Flask for the web server
- **Frontend**: HTML, CSS, and JavaScript
- **Terminal**: xterm.js with FitAddon and WebLinksAddon
- **SSH**: Paramiko for secure SSH connections
- **LLM Integration**: Google Gemini models for the chat interface
- **Data**: JSON-based storage for level information

The application follows good software engineering practices:

- Modular design with clear separation of concerns
- Comprehensive error handling
- Detailed logging
- Well-documented code
- Clean, maintainable structure

## Roadmap for Future Development

We have exciting plans for future versions of BanditGUI:

1. **Password Management**: Adding secure password storage with encryption
2. **Progress Tracking**: Implementing a system to track user progress through the challenges
3. **Gamification**: Adding badges, streaks, and other gamification elements to increase engagement
4. **Accessibility Improvements**: Ensuring proper contrast ratios, keyboard navigation, and screen reader compatibility

## Getting Started

Ready to try BanditGUI? Installation is now easier than ever with our automated installation scripts!

### Automatic Installation

#### Windows

1. Double-click on `install.bat`
2. Follow the on-screen instructions
3. After installation, run `run.bat` to start the application

#### Linux/macOS

1. Open a terminal in the project directory

2. Make the installation script executable:

   ```bash
   chmod +x install.sh
   ```

3. Run the installation script:

   ```bash
   ./install.sh
   ```

4. After installation, run the application:

   ```bash
   ./run.sh
   ```

### Manual Installation

If you prefer to install manually:

1. Clone the repository:

   ```bash
   git clone https://github.com/therealfredp3D/Making-BanditGUI.git
   cd Making-BanditGUI
   ```

2. Create and activate a virtual environment:

   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate

   # Linux/macOS
   python3 -m venv venv
   source venv/bin/activate
   ```